# SolidWorks Part File: Main Frame Plate
# This is a template/reference for creating the main frame plate in SolidWorks

## Design Intent
Create a central mounting plate for the quadcopter frame that provides:
- Structural rigidity for the entire frame
- Mounting points for flight controller, ESCs, and battery
- Attachment points for the four frame arms
- Cable management features
- Access holes for wiring

## Modeling Steps in SolidWorks

### 1. Base Sketch (Front Plane)
- Create a 200mm x 200mm square
- Add corner fillets: R10mm for aesthetics and stress reduction
- Center the sketch on the origin

### 2. Extrude Base Plate
- Extrude the base sketch: 3mm thick (for carbon fiber) or 2mm (for aluminum)
- Direction: Mid-plane to keep centered on origin

### 3. Frame Arm Mounting Features
Create 4 mounting tabs for frame arms:
- Position: 45° from each axis, 70mm from center
- Size: 25mm x 15mm rectangular tabs
- Thickness: Same as base plate
- Mounting holes: 2x M3 holes per tab, 16mm spacing

### 4. Flight Controller Mount
- Position: Center of plate
- Create raised platform: 40mm x 40mm x 2mm
- Mounting holes: 4x M3 holes in 30.5mm x 30.5mm pattern
- Add vibration dampening recesses if needed

### 5. ESC Mounting Areas
Create 4 ESC mounting areas:
- Position: Between frame arm mounts
- Size: 30mm x 15mm each
- Mounting: 2x M3 holes per ESC location
- Consider heat dissipation features

### 6. Battery Mount
- Position: Underside center (or top, depending on design)
- Create battery strap channels: 2mm wide x 1mm deep
- Add anti-slip texture or pads
- Ensure CG (center of gravity) alignment

### 7. Cable Management
- Create cable routing channels: 3mm wide x 1mm deep
- Add cable tie mounting points: small holes or slots
- Ensure clean wire runs to all components

### 8. Weight Reduction (Optional)
- Add lightening holes in non-critical areas
- Use hexagonal or circular patterns
- Maintain structural integrity
- Calculate weight savings vs. strength trade-offs

### 9. Finishing Features
- Add part number engraving
- Create assembly reference marks
- Add material specification callouts

## Material Properties (for Simulation)
### Carbon Fiber (3mm)
- Density: 1.6 g/cm³
- Tensile Strength: 600 MPa
- Young's Modulus: 70 GPa
- Weight: ~96g (estimated)

### Aluminum 6061-T6 (2mm)
- Density: 2.7 g/cm³
- Tensile Strength: 310 MPa
- Young's Modulus: 69 GPa
- Weight: ~162g (estimated)

## Design Validation
- Perform FEA stress analysis under flight loads
- Check deflection under maximum payload
- Verify mounting hole strength
- Validate vibration characteristics

## Manufacturing Notes
- CNC machining recommended for precision
- Deburr all edges
- Apply protective coating if aluminum
- Quality check all mounting hole positions
- Test fit with actual components before final production

## Assembly Notes
- Use thread locker on all fasteners
- Torque specifications: M3 screws to 1.2 Nm
- Install vibration dampeners for flight controller
- Route wires through designated channels
- Balance check after component installation
