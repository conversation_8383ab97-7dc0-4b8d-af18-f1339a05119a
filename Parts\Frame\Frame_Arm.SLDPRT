# SolidWorks Part File: Frame Arm
# Template for creating quadcopter frame arms

## Design Intent
Create lightweight, strong frame arms that:
- Connect motors to the main frame plate
- Provide optimal motor spacing (450mm diagonal)
- Minimize weight while maintaining structural integrity
- Allow for easy motor mounting and maintenance
- Include wire routing for motor cables

## Modeling Steps in SolidWorks

### 1. Base Profile Sketch (Front Plane)
- Create rectangular profile: 16mm wide x 8mm tall
- Length: 125mm (center of main plate to motor center)
- Add corner fillets: R1mm for manufacturing ease

### 2. Extrude Base Arm
- Extrude the profile sketch along its length
- Ensure proper orientation for mounting

### 3. Main Plate Connection End
Create mounting interface:
- Flat mounting surface: 25mm x 15mm
- Mounting holes: 2x M3 through holes, 16mm spacing
- Countersink holes for flush mounting screws
- Add alignment features if needed

### 4. Motor Mount End
Design motor mounting platform:
- Mounting plate: 25mm x 25mm x 3mm thick
- Motor bolt pattern: 19mm x 19mm (standard)
- Mounting holes: 4x M3 threaded holes
- Center hole: 8mm diameter for motor shaft clearance
- Ensure perpendicular alignment to arm axis

### 5. Wire Management
Create cable routing features:
- Cable channel: 3mm wide x 2mm deep along bottom of arm
- Entry/exit points at both ends
- Smooth transitions to prevent wire damage
- Consider removable cover for maintenance access

### 6. Weight Reduction
Optimize for weight while maintaining strength:
- Create lightening slots in non-critical areas
- Use oval or elongated holes
- Maintain minimum wall thickness: 2mm
- Perform stress analysis to validate design

### 7. Vibration Dampening
Add features to reduce vibration transmission:
- Consider rubber grommets in mounting holes
- Add vibration-dampening material interfaces
- Design for minimal resonance frequencies

### 8. Crash Protection
Design for impact resistance:
- Rounded edges and corners
- Breakaway features to protect motors
- Consider replaceable end caps
- Design for easy field repair

## Material Specifications

### Carbon Fiber Tube (Recommended)
- Wall thickness: 1.5-2mm
- Inner dimensions: 12mm x 4mm
- Outer dimensions: 16mm x 8mm
- Weight: ~15g per arm
- Excellent strength-to-weight ratio

### Aluminum Extrusion (Alternative)
- Wall thickness: 1.5mm
- Material: 6061-T6 aluminum
- Weight: ~25g per arm
- Lower cost, easier machining

## Manufacturing Options

### Option 1: Carbon Fiber Tube + Machined Ends
- Use pre-made carbon fiber rectangular tube
- Machine aluminum end pieces
- Bond with structural adhesive
- Lightest weight option

### Option 2: Full CNC Machining
- Machine entire arm from solid material
- Higher precision, integrated features
- More expensive but very durable

### Option 3: 3D Printed (Prototyping)
- Use for initial testing and fit checks
- Materials: PETG, ABS, or Nylon
- Not recommended for final flight version

## Design Validation

### Stress Analysis
- Apply motor thrust loads: 1000g per motor
- Check for deflection under load
- Verify safety factor > 3 for flight loads
- Consider dynamic loading and vibration

### Modal Analysis
- Check natural frequencies
- Avoid resonance with motor/propeller frequencies
- Target first mode > 50 Hz

## Assembly Instructions
1. Install motor mount end first
2. Route motor wires through cable channel
3. Apply thread locker to mounting screws
4. Torque main plate mounting screws to 1.5 Nm
5. Verify motor alignment before final tightening
6. Test motor rotation clearance
7. Secure wire routing with cable ties

## Quality Control
- Check all dimensions with calipers
- Verify mounting hole alignment
- Test fit with actual motors
- Inspect for cracks or defects
- Weigh each arm for consistency

## Maintenance Notes
- Inspect arms after hard landings
- Check mounting screw tightness regularly
- Replace if cracks or damage found
- Keep spare arms for quick field replacement
