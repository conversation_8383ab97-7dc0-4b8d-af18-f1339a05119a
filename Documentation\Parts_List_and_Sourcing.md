# Quadcopter Parts List and Sourcing Guide

## Custom Manufactured Parts (SolidWorks Designs)

### Frame Components
| Part Name | Quantity | Material | Manufacturing Method | Estimated Cost |
|-----------|----------|----------|---------------------|----------------|
| Main Frame Plate | 1 | Carbon Fiber 3mm | CNC/Waterjet | $25-40 |
| Frame Arms | 4 | Carbon Fiber Tube | CNC ends + tube | $15-25 each |
| Motor Mounts | 4 | Aluminum 6061-T6 | CNC Machining | $8-12 each |
| Landing Gear | 4 | Carbon Fiber/Aluminum | CNC/3D Print | $5-10 each |

**Total Custom Parts Cost: $150-250**

## Commercial Off-The-Shelf (COTS) Components

### Propulsion System
| Component | Specification | Quantity | Supplier Options | Price Range |
|-----------|---------------|----------|------------------|-------------|
| Brushless Motors | 2212 920KV or 2216 800KV | 4 | T-Motor, Sunnysky, Emax | $25-45 each |
| ESCs | 30A BLHeli_S/32 | 4 | Holybro, T-Motor, Tekko32 | $15-25 each |
| Propellers | 10x4.5 or 11x5 CF | 4 sets | APC, T-Motor, HQProp | $8-15 per set |

### Flight Control Electronics
| Component | Specification | Quantity | Supplier Options | Price Range |
|-----------|---------------|----------|------------------|-------------|
| Flight Controller | F4/F7 with OSD | 1 | Holybro, Matek, SpeedyBee | $40-80 |
| GPS Module | M8N/M9N with compass | 1 | Holybro, Matek, Here3 | $25-50 |
| Radio Receiver | 2.4GHz SBUS/PPM | 1 | FrSky, Spektrum, TBS | $20-40 |
| Telemetry Radio | 915MHz/433MHz | 1 | Holybro, RFD900, 3DR | $60-120 |

### Power System
| Component | Specification | Quantity | Supplier Options | Price Range |
|-----------|---------------|----------|------------------|-------------|
| LiPo Battery | 4S 4000-5000mAh | 1-2 | Tattu, Gens Ace, Turnigy | $40-80 each |
| Power Module | 60A with voltage sensing | 1 | Holybro, Matek, APM | $15-25 |
| Battery Strap | Velcro or rubber | 2 | Generic suppliers | $5-10 |

### Hardware and Fasteners
| Component | Specification | Quantity | Supplier Options | Price Range |
|-----------|---------------|----------|------------------|-------------|
| M3 Screws | Various lengths 6-25mm | 50 pcs | McMaster, Fastenal | $10-20 |
| M3 Nuts | Nylon lock nuts | 20 pcs | McMaster, Fastenal | $5-10 |
| M3 Washers | Flat and lock washers | 30 pcs | McMaster, Fastenal | $5-10 |
| Thread Locker | Blue Loctite 243 | 1 bottle | McMaster, Amazon | $8-12 |

## Recommended Suppliers

### Electronics and Motors
- **GetFPV** (USA): Wide selection, good support
- **RDQ (Race Day Quads)** (USA): Racing focus, quality parts
- **Banggood** (China): Budget options, longer shipping
- **HobbyKing** (Global): Good value, bulk options

### Materials and Manufacturing
- **DragonPlate** (USA): Carbon fiber sheets and tubes
- **McMaster-Carr** (USA): Hardware and raw materials
- **SendCutSend** (USA): Custom laser cutting and CNC
- **Protolabs** (Global): Rapid prototyping and manufacturing

### 3D Printing Services
- **Shapeways**: High-quality materials, various options
- **Craftcloud**: Price comparison across multiple services
- **Local Makerspaces**: Cost-effective for prototypes

## Cost Breakdown Summary

### Budget Build (~$400-500)
- Custom frame parts: $150
- Motors and ESCs: $160 (budget options)
- Electronics: $120 (basic flight controller setup)
- Battery and power: $60
- Hardware and misc: $30

### Performance Build (~$600-800)
- Custom frame parts: $200 (carbon fiber)
- Motors and ESCs: $240 (high-quality)
- Electronics: $180 (advanced flight controller, GPS)
- Battery and power: $100 (high-capacity battery)
- Hardware and misc: $50

### Professional Build (~$800-1200)
- Custom frame parts: $250 (precision manufacturing)
- Motors and ESCs: $320 (premium components)
- Electronics: $280 (redundant systems, telemetry)
- Battery and power: $150 (multiple batteries)
- Hardware and misc: $80

## Manufacturing Options

### DIY Manufacturing
**Pros:**
- Complete control over design
- Learning experience
- Customization flexibility
- Lower material costs

**Cons:**
- Requires tools and skills
- Time-intensive
- Quality consistency challenges
- Initial tooling investment

### Professional Manufacturing
**Pros:**
- High precision and quality
- Faster turnaround
- Professional finish
- Consistent results

**Cons:**
- Higher cost per part
- Minimum order quantities
- Less design flexibility
- Longer lead times

### Hybrid Approach (Recommended)
- 3D print prototypes for fit/function testing
- CNC machine critical structural parts
- Purchase COTS electronics and motors
- DIY assembly and integration

## Quality Considerations

### Critical Components (Don't Compromise)
- Flight controller and GPS
- Motors and ESCs
- Battery and power system
- Structural frame components

### Acceptable Budget Options
- Propellers (easy to replace)
- Hardware and fasteners
- Non-critical electronics
- Prototype/test components

## Lead Times and Planning

### Custom Manufacturing: 2-4 weeks
- Design finalization: 1 week
- Manufacturing: 1-2 weeks
- Quality control and shipping: 1 week

### COTS Components: 1-2 weeks
- Domestic suppliers: 3-7 days
- International suppliers: 1-3 weeks
- Expedited shipping available

### Assembly and Testing: 1 week
- Component preparation: 1-2 days
- Assembly: 2-3 days
- Testing and tuning: 2-3 days

**Total Project Timeline: 4-7 weeks**
